# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.env
.env.backup.*
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
data/logs/

# Database
*.db
*.sqlite

# Test coverage
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Cache
.cache/
data/cache/

# MediaCrawler - entire directory ignored
mediacrawler/

# MediaCrawler generated files
*_user_data_dir/
browser_data/
**/browser_data/
**/data/xhs/
*.crdownload
*.part

# Temporary files
*.tmp
*.temp
/tmp/

# Docker
.dockerignore

# Local config override
.env.local
config.local.py

# Claude Code Kit - Generated Files
claude-code-kit/
.claude/
.mcp/
.mcp.json
mcp-*.json
database.sqlite
*.cache
*.backup
*.bak
CLAUDE.md
rules/
debug_tools
tests

# Debug and test files
debug_*.py
test_*.py
check_*.py
simple_*.py
.env.backup.20250713_104157